// 解析結果の段落データ
export type ParagraphType = "lines" | "lyrics" | "directions";

export interface Paragraph {
  type: ParagraphType;
  header: string;
  content: string;
}

// PDFページデータ
export interface PDFPageData {
  page_number: number;
  image_url: string;
  width: number;
  height: number;
}

// 画像選択データ
export interface ImageSelectionData {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Vision API解析結果
export interface VisionAnalysisResult {
  paragraphs: Paragraph[];
  confidence: number;
  processing_time: number;
}

// Word文書生成オプション
export interface WordGenerationOptions {
  charactersPerLine: number;
  verticalWriting: boolean;
  fontSize: number;
  fontFamily: string;
}

// 認証関連
export interface LoginRequest {
  username: string;
  password: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  created_at: string;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<void>;
  refresh: () => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}
