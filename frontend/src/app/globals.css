@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --border: #e5e7eb;
  --input: #ffffff;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #374151;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #1a1a1a;
  --card-foreground: #ededed;
  --border: #2a2a2a;
  --input: #1a1a1a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #2a2a2a;
  --secondary-foreground: #d1d5db;
  --muted: #1a1a1a;
  --muted-foreground: #9ca3af;
  --accent: #2a2a2a;
  --accent-foreground: #d1d5db;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

.mincho {
  font-family: "MS Mincho", "ＭＳ 明朝", serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* 縦書きスタイル */
  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
  }

  .vertical-text-mixed {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }

  /* 縦書きグリッド用スタイル */
  .kabuki-grid {
    writing-mode: vertical-rl;
    direction: rtl;
  }

  .kabuki-column {
    writing-mode: horizontal-tb;
    direction: ltr;
  }
}
